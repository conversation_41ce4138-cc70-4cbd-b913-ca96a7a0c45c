{% extends 'base.html' %}

{% block head %}
<style>
    .table-container {
        @apply overflow-x-auto bg-base-100 rounded-lg shadow-lg mt-8;
    }

    .table-header {
        @apply sticky top-0 bg-base-200 z-10;
    }

    .numeric-cell {
        @apply font-mono text-right;
    }

    .trade-time {
        @apply text-sm text-base-content/70;
    }
</style>
{% endblock %}

{% block content %}
<div class="w-full">
    <!-- Header Section -->
    <div class="mb-6 flex justify-between items-center">
        <div>
            <h1 class="text-3xl font-bold">Trade Book</h1>
            <p class="text-base-content/60">View your executed trades</p>
        </div>
        <a href="{{ url_for('orders_bp.export_tradebook') }}" class="btn btn-primary">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd" />
            </svg>
            Export to CSV
        </a>
    </div>

    <!-- Trade Summary -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="stat">
            <div class="stat-title">Total Trades</div>
            <div class="stat-value">{{ tradebook_data|length }}</div>
            <div class="stat-desc">All executed trades</div>
        </div>
        
        <div class="stat">
            <div class="stat-title">Buy Trades</div>
            <div class="stat-value text-success">
                {{ tradebook_data|selectattr('action', 'equalto', 'BUY')|list|length }}
            </div>
            <div class="stat-desc">
                <div class="badge badge-success gap-2">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
                    </svg>
                    Long positions
                </div>
            </div>
        </div>
        
        <div class="stat">
            <div class="stat-title">Sell Trades</div>
            <div class="stat-value text-error">
                {{ tradebook_data|selectattr('action', 'equalto', 'SELL')|list|length }}
            </div>
            <div class="stat-desc">
                <div class="badge badge-error gap-2">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                    </svg>
                    Short positions
                </div>
            </div>
        </div>
    </div>

    <!-- Trades Table -->
    <div class="table-container">
        <table class="table w-full">
            <thead class="table-header">
                <tr>
                    <th class="cursor-pointer hover:bg-base-300">
                        Trading Symbol
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline-block ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4" />
                        </svg>
                    </th>
                    <th>Exchange</th>
                    <th>Product Type</th>
                    <th>Transaction Type</th>
                    <th>Fill Size</th>
                    <th>Fill Price</th>
                    <th>Trade Value</th>
                    <th>Order ID</th>
                    <th>Fill Time</th>
                </tr>
            </thead>
            <tbody>
                {% for trade in tradebook_data %}
                <tr class="hover:bg-base-200">
                    <td class="font-medium">{{ trade.symbol }}</td>
                    <td>
                        {% set exchange_colors = {
                            'NSE': 'badge-accent',
                            'BSE': 'badge-neutral',
                            'NFO': 'badge-secondary',
                            'MCX': 'badge-primary'
                        } %}
                        <div class="badge {{ exchange_colors.get(trade.exchange, 'badge-ghost') }}">
                            {{ trade.exchange }}
                        </div>
                    </td>
                    <td>
                        {% set product_type_colors = {
                            'CNC': 'badge-secondary',
                            'MIS': 'badge-accent',
                            'NRML': 'badge-neutral'
                        } %}
                        <div class="badge {{ product_type_colors.get(trade.product, 'badge-ghost') }}">
                            {{ trade.product }}
                        </div>
                    </td>
                    <td>
                        <div class="badge {% if trade.action == 'BUY' %}badge-success{% else %}badge-error{% endif %} gap-2">
                            {% if trade.action == 'BUY' %}
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
                            </svg>
                            {% else %}
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                            </svg>
                            {% endif %}
                            {{ trade.action }}
                        </div>
                    </td>
                    <td class="numeric-cell">{{ trade.quantity }}</td>
                    <td class="numeric-cell">{{ trade.average_price }}</td>
                    <td class="numeric-cell">{{ trade.trade_value }}</td>
                    <td class="font-mono text-sm">{{ trade.orderid }}</td>
                    <td class="trade-time">{{ trade.timestamp }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <!-- Pagination -->
    <div class="flex justify-end mt-6">
        <div class="join">
            <button class="join-item btn btn-sm">«</button>
            <button class="join-item btn btn-sm">Page 1</button>
            <button class="join-item btn btn-sm">»</button>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add sorting functionality to table headers
    document.querySelectorAll('th.cursor-pointer').forEach(headerCell => {
        headerCell.addEventListener('click', () => {
            const table = headerCell.closest('table');
            const tbody = table.querySelector('tbody');
            const rows = Array.from(tbody.querySelectorAll('tr'));
            const index = Array.from(headerCell.parentElement.children).indexOf(headerCell);
            
            // Get current sort direction
            const currentDirection = headerCell.getAttribute('data-sort') || 'asc';
            const newDirection = currentDirection === 'asc' ? 'desc' : 'asc';
            
            // Update sort direction
            headerCell.setAttribute('data-sort', newDirection);
            
            // Sort rows
            rows.sort((a, b) => {
                const aValue = a.children[index].textContent.trim();
                const bValue = b.children[index].textContent.trim();
                
                // Check if values are numbers
                const aNum = parseFloat(aValue);
                const bNum = parseFloat(bValue);
                
                if (!isNaN(aNum) && !isNaN(bNum)) {
                    return newDirection === 'asc' ? aNum - bNum : bNum - aNum;
                }
                
                // Handle date sorting for timestamp column
                if (index === 8) {  // Timestamp column index
                    const aDate = new Date(aValue);
                    const bDate = new Date(bValue);
                    return newDirection === 'asc' ? aDate - bDate : bDate - aDate;
                }
                
                return newDirection === 'asc' 
                    ? aValue.localeCompare(bValue)
                    : bValue.localeCompare(aValue);
            });
            
            // Clear and repopulate tbody
            tbody.innerHTML = '';
            rows.forEach(row => tbody.appendChild(row));
        });
    });
});
</script>
{% endblock %}
