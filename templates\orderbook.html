{% extends "base.html" %}

{% block head %}
<style>
    .stat-card {
        @apply bg-base-100 rounded-lg shadow-lg p-6 transition-all duration-300;
    }
    
    .stat-card:hover {
        @apply shadow-xl transform -translate-y-1;
    }

    .table-container {
        @apply overflow-x-auto bg-base-100 rounded-lg shadow-lg;
    }

    .table-header {
        @apply sticky top-0 bg-base-200 z-10;
    }

    .numeric-cell {
        @apply font-mono text-right;
    }
</style>
{% endblock %}

{% block content %}
<div class="w-full">
    <!-- Header Section -->
    <div class="mb-6 flex justify-between items-center">
        <div>
            <h1 class="text-3xl font-bold">Order Book</h1>
            <p class="text-base-content/60">Track and manage your trading orders</p>
        </div>
        <div class="flex gap-2">
            <button onclick="openCancelAllModal()" class="btn btn-error">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                </svg>
                Cancel All Orders
            </button>
            <a href="{{ url_for('orders_bp.export_orderbook') }}" class="btn btn-primary">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd" />
                </svg>
                Export to CSV
            </a>
        </div>
    </div>

    <!-- Stats Grid -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
        <!-- Buy Orders -->
        <div class="stat-card">
            <div class="stat">
                <div class="stat-title">Buy Orders</div>
                <div class="stat-value text-success">{{ order_stats.total_buy_orders }}</div>
                <div class="stat-desc mt-2">
                    <div class="badge badge-success">Long Positions</div>
                </div>
            </div>
        </div>

        <!-- Sell Orders -->
        <div class="stat-card">
            <div class="stat">
                <div class="stat-title">Sell Orders</div>
                <div class="stat-value text-error">{{ order_stats.total_sell_orders }}</div>
                <div class="stat-desc mt-2">
                    <div class="badge badge-error">Short Positions</div>
                </div>
            </div>
        </div>

        <!-- Completed Orders -->
        <div class="stat-card">
            <div class="stat">
                <div class="stat-title">Completed Orders</div>
                <div class="stat-value text-success">{{ order_stats.total_completed_orders }}</div>
                <div class="stat-desc mt-2">
                    <div class="badge badge-success">Executed</div>
                </div>
            </div>
        </div>

        <!-- Open Orders -->
        <div class="stat-card">
            <div class="stat">
                <div class="stat-title">Open Orders</div>
                <div class="stat-value text-warning">{{ order_stats.total_open_orders }}</div>
                <div class="stat-desc mt-2">
                    <div class="badge badge-warning">Pending</div>
                </div>
            </div>
        </div>

        <!-- Rejected Orders -->
        <div class="stat-card">
            <div class="stat">
                <div class="stat-title">Rejected Orders</div>
                <div class="stat-value text-error">{{ order_stats.total_rejected_orders }}</div>
                <div class="stat-desc mt-2">
                    <div class="badge badge-error">Failed</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Orders Table -->
    <div id="orderbook-table-container" class="table-container">
        <table class="table w-full">
            <thead class="table-header">
                <tr>
                    <th class="cursor-pointer hover:bg-base-300">
                        Trading Symbol
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline-block ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4" />
                        </svg>
                    </th>
                    <th>Exchange</th>
                    <th>Transaction Type</th>
                    <th>Quantity</th>
                    <th>Price</th>
                    <th>Trigger Price</th>
                    <th>Order Type</th>
                    <th>Product Type</th>
                    <th>Order ID</th>
                    <th>Status</th>
                    <th>Time</th>
                </tr>
            </thead>
            <tbody>
                {% for order in order_data %}
                <tr class="hover:bg-base-200">
                    <td class="font-medium">{{ order.symbol }}</td>
                    <td>
                        {% set exchange_colors = {
                            'NSE': 'badge-accent',
                            'BSE': 'badge-neutral',
                            'NFO': 'badge-secondary',
                            'MCX': 'badge-primary'
                        } %}
                        <div class="badge {{ exchange_colors.get(order.exchange, 'badge-ghost') }}">
                            {{ order.exchange }}
                        </div>
                    </td>
                    <td>
                        <div class="badge {% if order.action == 'BUY' %}badge-success{% else %}badge-error{% endif %} gap-2">
                            {% if order.action == 'BUY' %}
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
                            </svg>
                            {% else %}
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                            </svg>
                            {% endif %}
                            {{ order.action }}
                        </div>
                    </td>
                    <td class="numeric-cell">{{ order.quantity }}</td>
                    <td class="numeric-cell">{{ order.price }}</td>
                    <td class="numeric-cell">{{ order.trigger_price }}</td>
                    <td>
                        {% set order_type_colors = {
                            'MARKET': 'badge-primary',
                            'LIMIT': 'badge-info',
                            'SL': 'badge-warning',
                            'SL-M': 'badge-warning'
                        } %}
                        <div class="badge {{ order_type_colors.get(order.pricetype, 'badge-ghost') }}">
                            {{ order.pricetype }}
                        </div>
                    </td>
                    <td>
                        {% set product_type_colors = {
                            'CNC': 'badge-secondary',
                            'MIS': 'badge-accent',
                            'NRML': 'badge-neutral'
                        } %}
                        <div class="badge {{ product_type_colors.get(order.product, 'badge-ghost') }}">
                            {{ order.product }}
                        </div>
                    </td>
                    <td class="font-mono text-sm">{{ order.orderid }}</td>
                    <td>
                        {% set status_colors = {
                            'complete': 'badge-success',
                            'rejected': 'badge-error',
                            'cancelled': 'badge-error',
                            'open': 'badge-warning',
                            'pending': 'badge-warning',
                            'trigger pending': 'badge-info'
                        } %}
                        <div class="badge {{ status_colors.get(order.order_status|lower, 'badge-ghost') }}">
                            {{ order.order_status }}
                        </div>
                    </td>
                    <td class="text-sm">{{ order.timestamp }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add sorting functionality to table headers
    document.querySelectorAll('th.cursor-pointer').forEach(headerCell => {
        headerCell.addEventListener('click', () => {
            const table = headerCell.closest('table');
            const tbody = table.querySelector('tbody');
            const rows = Array.from(tbody.querySelectorAll('tr'));
            const index = Array.from(headerCell.parentElement.children).indexOf(headerCell);
            
            // Get current sort direction
            const currentDirection = headerCell.getAttribute('data-sort') || 'asc';
            const newDirection = currentDirection === 'asc' ? 'desc' : 'asc';
            
            // Update sort direction
            headerCell.setAttribute('data-sort', newDirection);
            
            // Sort rows
            rows.sort((a, b) => {
                const aValue = a.children[index].textContent.trim();
                const bValue = b.children[index].textContent.trim();
                
                // Check if values are numbers
                const aNum = parseFloat(aValue);
                const bNum = parseFloat(bValue);
                
                if (!isNaN(aNum) && !isNaN(bNum)) {
                    return newDirection === 'asc' ? aNum - bNum : bNum - aNum;
                }
                
                return newDirection === 'asc' 
                    ? aValue.localeCompare(bValue)
                    : bValue.localeCompare(aValue);
            });
            
            // Clear and repopulate tbody
            tbody.innerHTML = '';
            rows.forEach(row => tbody.appendChild(row));
        });
    });
});

// Cancel all orders functions
let isCanceling = false; // Flag to prevent multiple calls
let reloadTimer = null; // Store the reload timer

// Add a global flag to prevent any refresh attempts
window.CANCEL_ALL_IN_PROGRESS = false;

// Override the refreshCurrentPageContent to check our flag
const _originalRefresh = window.refreshCurrentPageContent;
window.refreshCurrentPageContent = function() {
    if (window.CANCEL_ALL_IN_PROGRESS) {
        console.log('Refresh blocked - cancel all in progress');
        return;
    }
    if (_originalRefresh) {
        _originalRefresh();
    }
};

function cancelAllOrders() {
    // Prevent multiple simultaneous calls
    if (isCanceling) {
        return;
    }
    
    isCanceling = true;
    window.CANCEL_ALL_IN_PROGRESS = true;
    
    // Disable the confirm button
    const confirmBtn = document.getElementById('cancel-all-confirm-btn');
    if (confirmBtn) {
        confirmBtn.disabled = true;
        confirmBtn.textContent = 'Canceling...';
    }
    
    // Close the modal
    document.getElementById('cancel-all-modal').checked = false;
    
    // Show processing message
    showToast('Canceling all orders... Please wait', 'info');
    
    // Call the cancel_all_orders endpoint
    fetch('/cancel_all_orders', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCSRFToken()
        },
        body: JSON.stringify({})
    })
    .then(response => response.json())
    .then(data => {
        // Show toast notification
        if (data.status === 'success') {
            showToast(data.message, 'success');
        } else if (data.status === 'info') {
            showToast(data.message, 'info');
        } else {
            showToast(data.message, 'error');
            isCanceling = false;
            window.CANCEL_ALL_IN_PROGRESS = false;
            // Re-enable socket events on error
            setTimeout(() => location.reload(), 2000);
            return;
        }
        
        // Set a single reload timer for 10 seconds
        reloadTimer = setTimeout(() => {
            // Create a form and submit it to force a clean GET request
            const form = document.createElement('form');
            form.method = 'GET';
            form.action = '/orderbook';
            document.body.appendChild(form);
            form.submit();
        }, 10000); // 10 seconds delay to ensure rate limit is respected
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('An error occurred while canceling orders.', 'error');
        isCanceling = false;
        window.CANCEL_ALL_IN_PROGRESS = false;
        // Reload on error
        setTimeout(() => location.reload(), 2000);
    });
}

function openCancelAllModal() {
    document.getElementById('cancel-all-modal').checked = true;
}
</script>

<!-- DaisyUI Modal for Cancel All confirmation -->
<input type="checkbox" id="cancel-all-modal" class="modal-toggle" />
<div class="modal" role="dialog">
    <div class="modal-box">
        <h3 class="font-bold text-lg text-error">⚠️ Cancel All Orders</h3>
        <p class="py-4">Are you sure you want to cancel all open orders? This action will cancel all your pending orders immediately.</p>
        <div class="modal-action">
            <label for="cancel-all-modal" class="btn">Cancel</label>
            <button id="cancel-all-confirm-btn" onclick="cancelAllOrders()" class="btn btn-error">Yes, Cancel All</button>
        </div>
    </div>
    <label class="modal-backdrop" for="cancel-all-modal"></label>
</div>
{% endblock %}
